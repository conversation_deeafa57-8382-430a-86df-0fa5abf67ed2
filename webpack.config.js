const path = require('path');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const webpack = require('webpack');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    mode: isProduction ? 'production' : 'development',
    entry: './src/index.js',
    output: {
      filename: 'bundle.js',
      path: path.resolve(__dirname, './'),
      publicPath: './',
      clean: false // لا نريد حذف الملفات الأخرى في المجلد الجذر
    },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource',
      },
    ],
  },
  resolve: {
    extensions: ['.js', '.jsx'],
    fallback: {
      "process/browser": require.resolve("process/browser"),
      "path": require.resolve("path-browserify"),
      "fs": false,
      "crypto": require.resolve("crypto-browserify"),
      "stream": require.resolve("stream-browserify"),
      "buffer": require.resolve("buffer/"),
      "util": require.resolve("util/"),
      "assert": require.resolve("assert/"),
      "http": require.resolve("stream-http"),
      "https": require.resolve("https-browserify"),
      "os": require.resolve("os-browserify/browser"),
      "url": require.resolve("url/"),
      "zlib": require.resolve("browserify-zlib"),
      "module": false,
      "electron": false
    },
    alias: {
      // استخدام نسخة UMD من المكتبات المشكلة
      'jspdf': path.resolve(__dirname, 'node_modules/jspdf/dist/jspdf.umd.min.js'),
      'process': require.resolve('process/browser')
    }
  },
  target: 'electron-renderer',
  devtool: isProduction ? 'source-map' : 'eval-source-map',
  optimization: {
    minimize: isProduction,
    splitChunks: false // نريد ملف واحد للتطبيق
  },
  plugins: [
    new NodePolyfillPlugin(),
    new webpack.DefinePlugin({
      'global': 'window',
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      'process': 'process',
      'module': JSON.stringify({
        createRequire: function() {
          return function() { return {}; }
        }
      })
    }),
    new webpack.ProvidePlugin({
      process: require.resolve('process/browser'),
      Buffer: ['buffer', 'Buffer'],
      React: 'react',
      ReactDOM: 'react-dom',
      'module.createRequire': function() {
        return function() { return {}; }
      }
    })
  ],
  externals: {
    'better-sqlite3': 'commonjs better-sqlite3',
    'sqlite3': 'commonjs sqlite3',
    'electron': 'commonjs electron',
    'fs': 'commonjs fs',
    'path': 'commonjs path'
  }
  };
};
