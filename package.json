{"name": "warehouse-management-system", "version": "1.1.1", "description": "A comprehensive Warehouse Management System built with Electron and React", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"webpack --watch\" \"electron . --dev\"", "build": "webpack --mode production && electron-builder", "pack": "webpack --mode production && electron-builder --dir", "dist": "webpack --mode production && electron-builder", "dist:win": "webpack --mode production && electron-builder --win --publish never", "portable": "webpack --mode production && electron-builder --win portable --publish never", "build-installer": "node build-installer.js", "build-minimal": "node build-minimal.js", "test-build": "node test-build-readiness.js", "free-space": "npm cache clean --force && npm run clean", "clean": "rimraf dist bundle.js bundle.js.map", "prebuild": "npm run clean", "postinstall": "electron-builder install-app-deps", "start-dev": "cross-env NODE_ENV=development electron ."}, "keywords": ["warehouse", "inventory", "management", "electron", "react"], "author": "", "license": "MIT", "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@electron/rebuild": "^4.0.1", "babel-loader": "^10.0.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "electron": "^35.2.1", "electron-builder": "^24.9.1", "electron-packager": "^17.1.2", "electron-rebuild": "^3.2.9", "rimraf": "^5.0.5", "sharp": "^0.34.1", "style-loader": "^4.0.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1"}, "dependencies": {"adm-zip": "^0.5.16", "assert": "^2.1.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "body-parser": "^2.2.0", "browserify-shim": "^3.8.16", "buffer": "^6.0.3", "chart.js": "^4.4.0", "cors": "^2.8.5", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "express": "^5.1.0", "file-saver": "^2.0.5", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "node-polyfill-webpack-plugin": "^4.1.0", "node-schedule": "^2.1.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react": "17.0.2", "react-autosuggest": "^10.1.0", "react-chartjs-2": "^5.2.0", "react-dom": "17.0.2", "react-icons": "^4.11.0", "react-router-dom": "^6.18.0", "scheduler": "0.20.2", "sqlite3": "^5.1.7", "stream-browserify": "^3.0.0", "util": "^0.12.5", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "build": {"appId": "com.hgroup.wms", "productName": "نظام إدارة المخازن", "copyright": "Copyright © 2025 H Group", "directories": {"output": "build-output", "buildResources": "build"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!**/test*.js", "!**/fix*.js", "!**/quick*.js", "!**/debug*.js", "!**/diagnose*.js", "!**/*.md", "!src/**/*", "bundle.js", "bundle.js.map", "main.js", "preload.js", "index.html", "package.json", "assets/**/*", "wms-database/**/*", "*.js", "!webpack.config.js", "!babel.config.js"], "extraResources": [{"from": "wms-database", "to": "wms-database", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة المخازن", "artifactName": "نظام-إدارة-المخازن-${version}-installer.${ext}", "menuCategory": "H Group", "displayLanguageSelector": false, "language": "1025", "perMachine": false, "deleteAppDataOnUninstall": false, "runAfterFinish": true, "include": "build/installer.nsh"}, "portable": {"artifactName": "نظام-إدارة-المخازن-${version}-portable.${ext}"}, "publish": null}}